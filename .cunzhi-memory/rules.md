# 开发规范和规则

- 修复jTDS驱动兼容性问题：在JdbcServiceImpl的configureHikariPool方法中添加jTDS驱动检测，设置connectionTestQuery为"SELECT 1"以避免AbstractMethodError
- 调整HikariCP连接泄漏检测阈值：从5秒增加到30秒，避免复杂查询时的误报警告
- 连接泄漏问题根因：pageListEdb使用JdbcService.getPooledConnection()获取ConnectionWrapper包装的连接，而testConnection直接使用UtsDatabaseService.getDataSource()获取原始DataSource，应统一使用后者避免连接包装问题
- 修复数据库类型识别错误：UtsFreereportsServiceImpl中不应写死setSourcetype为MYSQL，应根据jdbcUrl动态判断是MySQL还是SQL Server
- 修改pageListEdb方法使用数据库分页：使用getTotalCount方法获取真实总数，使用buildPagedSql构建分页SQL，避免内存分页导致的总数不准确问题
- 优化分页查询性能：使用OFFSET...FETCH语法替代ROW_NUMBER()，移除COUNT查询中的ORDER BY子句，统一使用NamedParameterJdbcTemplate连接方式，避免重复获取数据源信息
